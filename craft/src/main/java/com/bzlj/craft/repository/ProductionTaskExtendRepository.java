package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.ProductionTaskExtend;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ProductionTaskExtendRepository extends BaseRepository<ProductionTaskExtend, String> {

    List<ProductionTaskExtend> findByTaskTaskIdAndDeleted(String taskId, <PERSON><PERSON><PERSON> deleted);
}