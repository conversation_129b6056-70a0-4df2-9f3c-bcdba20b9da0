package com.bzlj.score.service.impl;

import com.bici.casdoor.entity.User;
import com.bici.casdoor.service.UserService;
import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.result.DataResult;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.base.search.SortItem;
import com.bzlj.base.util.DTOConverter;
import com.bzlj.craft.vo.surveillance.ProcessScoreVO;
import com.bzlj.score.dto.DeviationDegreeConfigDTO;
import com.bzlj.score.dto.ProcessScoreDTO;
import com.bzlj.score.entity.DeviationDegreeConfig;
import com.bzlj.score.entity.ProcessScore;
import com.bzlj.score.repository.DeviationDegreeConfigRepository;
import com.bzlj.score.repository.ProcessScoreRepository;
import com.bzlj.score.service.IProcessScoreService;
import com.bzlj.score.util.JpaUtil;
import com.bzlj.score.util.JsonSchemaUtil;
import com.bzlj.score.util.VersionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.networknt.schema.ValidationMessage;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.bzlj.score.exception.BiciErrorData.*;

/**
 * <AUTHOR>
 * @description: 工序评分
 * @date 2025-03-10 13:38
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProcessScoreServiceImpl implements IProcessScoreService {

    private static final ObjectMapper objectMapper = new ObjectMapper();


    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public BaseRepository<ProcessScore, String> getRepository() {
        return processScoreRepository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<ProcessScoreDTO> getDTOClass() {
        return ProcessScoreDTO.class;
    }

    @Override
    public Class<ProcessScore> getPOClass() {
        return ProcessScore.class;
    }


    @Autowired
    private ProcessScoreRepository processScoreRepository;

    @Autowired
    private DeviationDegreeConfigRepository deviationDegreeConfigRepository;

    @Autowired
    private UserService userService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProcessScoreDTO saveProcessScore(ProcessScoreDTO processScoreDTO) {
        boolean flag = checkDetail(processScoreDTO.getRuleDetail());
        if (!flag) {
            throw SCORE_DETAIL_FORMAT_ERROR.buildException();
        }
        //上一个版本latest置为false
        String id = processScoreDTO.getId();
        ProcessScore score = new ProcessScore();
        if (!StringUtils.isEmpty(id)) {
            score = processScoreRepository.findById(id).orElse(null);
            if (Objects.isNull(score)) {
                throw new RuntimeException("数据不存在，更新失败");
            }
            if (BooleanUtils.isTrue(score.getEnableFlag())) {
                //升版
                processScoreDTO.setVersion(VersionUtil.generateVersion(processScoreDTO.getVersion()));
                processScoreDTO.setParentId(id);
                processScoreDTO.setId(null);
                processScoreDTO.setEnableFlag(true);
                score.setLatest(false);
                processScoreRepository.save(score);
            }
        } else {
            processScoreDTO.setVersion(VersionUtil.generateVersion(processScoreDTO.getVersion()));
        }
        ProcessScore processScore = new ProcessScore();
        processScoreDTO.setLatest(true);

        BeanUtils.copyProperties(processScoreDTO, processScore);
        ProcessScore save = null;
        if (StringUtils.isEmpty(processScore.getId())) {
            save = processScoreRepository.save(processScore);
        } else {
            //复制更新字段
            BeanUtils.copyProperties(processScore, score, JpaUtil.getNullPropertyNames(processScore));
            save = processScoreRepository.save(score);
        }
        return convertToDto(save, ProcessScoreDTO.class);
    }

    private boolean checkDetail(List<Map<String, Object>> ruleDetail) {
        if (CollectionUtils.isEmpty(ruleDetail)) {
            throw SCORE_DETAIL_CANNOT_NULL.buildException();
        }
        String json = null;
        try {
            json = objectMapper.writeValueAsString(ruleDetail);
        } catch (JsonProcessingException e) {
            throw SCORE_DETAIL_ANALYSIS_FAIL.buildException();
        }
        Set<ValidationMessage> validationMessages = JsonSchemaUtil.checkBySchema(json, "jsonSchema/score_schema.json");
        if (CollectionUtils.isEmpty(validationMessages)) {
            return true;
        }
        log.error("jsonSchema校验失败：{}", validationMessages.toString());
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult findProcessId(String processId) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSortItems(Lists.newArrayList(SortItem.builder().fieldName("updateAt").sortOrder(SortItem.DESC_ORDER_NAME).build()));
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("processId", processId, null, SearchItem.Operator.EQ))
                .item(new SearchItem("deleted", false, null, SearchItem.Operator.EQ))
                .item(new SearchItem("latest", true, null, SearchItem.Operator.EQ))
                .build());
        List<ProcessScoreDTO> processScores = this.findAllWithCondition(searchCondition);
        if (!CollectionUtils.isEmpty(processScores)) {
            List<List<Object>> value = DTOConverter.convert(Lists.newArrayList(processScores), ProcessScoreVO.class);
            dataResult.setList(value);
        }
        return dataResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessScoreDTO modifyScoreState(String id, ProcessScoreDTO scoreDTO) {
        if (Objects.isNull(scoreDTO.getEnableFlag())) {
            throw new RuntimeException("请选择启用状态");
        }
        ProcessScore score = processScoreRepository.findById(id).orElse(null);
        if (Objects.isNull(score)) {
            throw new RuntimeException("数据不存在，更新失败");
        }
        ProcessScore result = null;
        if (BooleanUtils.isFalse(scoreDTO.getEnableFlag())) {
            score.setEnableFlag(scoreDTO.getEnableFlag());
            result = processScoreRepository.save(score);
        } else {
            score.setLatest(false);
            processScoreRepository.save(score);
            ProcessScore processScore = new ProcessScore();
            BeanUtils.copyProperties(score, processScore, "id", "version", "latest");
            //启用升版
            processScore.setVersion(VersionUtil.generateVersion(score.getVersion()));
            processScore.setParentId(id);
            processScore.setId(null);

            processScore.setLatest(true);
            processScore.setEnableFlag(true);
            result = processScoreRepository.save(processScore);
        }
        return convertToDto(result, ProcessScoreDTO.class);
    }


    @Override
    public ProcessScoreDTO convertToDto(ProcessScore entity, Class<ProcessScoreDTO> dtoClass, String... ignoreProperties) {
        ProcessScoreDTO dto = createInstance(dtoClass);
        BeanUtils.copyProperties(entity, dto, ignoreProperties);
        //todo 后续获取用户信息使用缓存
        String updateUserId = entity.getUpdateUserId();
        try {
            User user = userService.getUser(updateUserId);
            dto.setUpdateUserName(user.getDisplayName());
        }catch (Exception e){
            log.error("获取用户信息失败:{}", e.getStackTrace(), e);
        }
        return dto;
    }

    @Override
    public List<DeviationDegreeConfigDTO> findDeviationDegreeConfig() {
        List<DeviationDegreeConfig> configs = deviationDegreeConfigRepository.findAll(Sort.by(Sort.Direction.ASC, "sortOrder"));
        if (CollectionUtils.isEmpty(configs)) return List.of();
        return configs.stream().map(config -> {
            DeviationDegreeConfigDTO configDTO = new DeviationDegreeConfigDTO();
            BeanUtils.copyProperties(config, configDTO);
            return configDTO;
        }).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logicDelete(String processScoreId) {
        ProcessScore score = processScoreRepository.findById(processScoreId).orElse(null);
        if (Objects.isNull(score)) {
            throw DATA_DELETE_FAIL.buildException();
        }
        score.setDeleted(true);
        processScoreRepository.save(score);
    }

    @Override
    public ProcessScoreDTO findProcessScoreById(String processScoreId) {
        ProcessScore score = processScoreRepository.findById(processScoreId).orElse(null);
        if (Objects.isNull(score)) {
            throw DATA_NOT_EXIST.buildException();
        }
        return convertToDto(score, ProcessScoreDTO.class);
    }
}



