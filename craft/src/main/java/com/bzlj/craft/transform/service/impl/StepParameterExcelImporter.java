package com.bzlj.craft.transform.service.impl;

import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.*;
import com.bzlj.craft.transform.constant.ExcelImportPolicyConstants;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.transform.service.ExcelImportPolicy;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service(value = ExcelImportPolicyConstants.EXCEL_IMPORT_POLICY + "stepParameter")
public class StepParameterExcelImporter implements ExcelImportPolicy<StepParameter> {

    private final CraftProcessRepository craftProcessRepository;
    private final ProcessStepRepository processStepRepository;
    private final StepParameterRepository stepParameterRepository;
    private final DataPrepareService dataPrepareService;

    // 缓存工序和工步的映射关系，避免重复查询
    private final Map<String, CraftProcess> processCache = new ConcurrentHashMap<>();
    private final Map<String, ProcessStep> stepCache = new ConcurrentHashMap<>();
    // 缓存工步下已有的参数，用于判断是否需要更新
    private final Map<String, Map<String, StepParameter>> stepParameterCache = new ConcurrentHashMap<>();

    public StepParameterExcelImporter(CraftProcessRepository craftProcessRepository,
                                      ProcessStepRepository processStepRepository,
                                      StepParameterRepository stepParameterRepository,
                                      DataPrepareService dataPrepareService) {
        this.craftProcessRepository = craftProcessRepository;
        this.processStepRepository = processStepRepository;
        this.stepParameterRepository = stepParameterRepository;
        this.dataPrepareService = dataPrepareService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult<StepParameter> importFromExcel(InputStream inputStream) {
        ImportResult<StepParameter> results = new ImportResult<>();

        List<StepParameter> stepParameters = parseExcel(inputStream);

        if (CollectionUtils.isEmpty(stepParameters)) {
            results.setSize(0);
            results.setResults(List.of());
            return results;
        }

        // 批量保存工艺参数（包括新增和更新）
        List<StepParameter> savedParameters = stepParameterRepository.saveAll(stepParameters);

        results.setSize(savedParameters.size());
        results.setResults(savedParameters);
        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StepParameter> parseExcel(InputStream inputStream) {
        List<StepParameter> stepParameters = new ArrayList<>();
        Workbook workbook = null;
        
        try {
            workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 默认读取第一个Sheet

            // 用于记录每个工步下参数的数量，用于设置paramOrder
            // 需要先查询数据库中已有的参数数量
            Map<String, Integer> stepParamCountMap = new ConcurrentHashMap<>();

            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // 跳过表头

                String processCode = getCellStringValue(row.getCell(0)); // 工序
                String paramCodeFromExcel = getCellStringValue(row.getCell(1));   // 参数Code（来自Excel）
                String paramName = getCellStringValue(row.getCell(2));   // 参数名称
                String paramType = getCellStringValue(row.getCell(3));   // 参数类型

                // 验证必填字段
                if (!StringUtils.hasText(processCode) || !StringUtils.hasText(paramCodeFromExcel)
                    || !StringUtils.hasText(paramName) || !StringUtils.hasText(paramType)) {
                    continue; // 跳过无效行
                }

                // 查询工序
                CraftProcess craftProcess = getCraftProcess(processCode);
                if (Objects.isNull(craftProcess)) {
                    continue; // 工序不存在，跳过
                }

                // 查询工步
                ProcessStep processStep = getProcessStep(craftProcess.getId());
                if (Objects.isNull(processStep)) {
                    continue; // 工步不存在，跳过
                }

                // 获取参数类型字典项
                SysDictItem paramTypeDictItem = dataPrepareService.getParamTypeDictItem(paramType);
                if (Objects.isNull(paramTypeDictItem)) {
                    continue; // 参数类型不存在，跳过
                }

                String stepId = processStep.getId();

                // 检查是否已存在相同paramCode的参数
                StepParameter existingParameter = getExistingParameter(stepId, paramCodeFromExcel);

                StepParameter stepParameter;
                if (existingParameter != null) {
                    // 更新现有参数
                    stepParameter = existingParameter;
                    stepParameter.setParamName(paramName);
                    stepParameter.setParamType(paramTypeDictItem);
                    // 保持原有的paramOrder和version
                } else {
                    // 创建新参数
                    stepParameter = new StepParameter();
                    stepParameter.setStep(processStep);
                    stepParameter.setParamCode(paramCodeFromExcel);
                    stepParameter.setParamName(paramName);
                    stepParameter.setParamType(paramTypeDictItem);

                    // 计算参数顺序
                    // 查询数据库中该工步已有的参数数量
                    int currentCount = stepParamCountMap.computeIfAbsent(stepId, stepParameterRepository::countByStepId);
                    int paramOrder = currentCount + 1;
                    stepParamCountMap.put(stepId, paramOrder);

                    stepParameter.setParamOrder(paramOrder);
                    stepParameter.setVersion(1); // 默认版本号
                }

                stepParameters.add(stepParameter);
            }
        } catch (IOException e) {
            throw new RuntimeException("解析Excel文件失败", e);
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    throw new RuntimeException("关闭Excel文件失败", e);
                }
            }
        }
        
        return stepParameters;
    }

    /**
     * 获取工序，使用缓存避免重复查询
     */
    private CraftProcess getCraftProcess(String processCode) {
        return processCache.computeIfAbsent(processCode, code -> 
            craftProcessRepository.findFirstByProcessCode(code));
    }

    /**
     * 获取工步，使用缓存避免重复查询
     */
    private ProcessStep getProcessStep(String processId) {
        return stepCache.computeIfAbsent(processId, id -> {
            List<ProcessStep> steps = processStepRepository.findByProcessIdOrderByStepOrderAsc(id);
            return CollectionUtils.isEmpty(steps) ? null : steps.get(0); // 取第一个工步
        });
    }

    /**
     * 获取已存在的参数，根据stepId和paramCode查询
     */
    private StepParameter getExistingParameter(String stepId, String paramCode) {
        Map<String, StepParameter> stepParams = stepParameterCache.computeIfAbsent(stepId, id -> new ConcurrentHashMap<>());

        return stepParams.computeIfAbsent(paramCode, code ->
            stepParameterRepository.findByStepIdAndParamCode(stepId, code));
    }
}
