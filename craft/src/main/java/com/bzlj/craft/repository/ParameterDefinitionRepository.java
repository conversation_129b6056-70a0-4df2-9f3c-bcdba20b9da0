package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.ParameterDefinition;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ParameterDefinitionRepository extends BaseRepository<ParameterDefinition, String> {
    List<ParameterDefinition> findByWorkStepWorkStepIdInAndParamType_ItemCodeIn(List<String> workStepIds,List<String> paramType);

    List<ParameterDefinition> findByWorkStepWorkStepId(String workStepId);
}