package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.Instant;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "material_source")
public class MaterialSource {
    @Id
    @Size(max = 36)
    @Column(name = "id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "material_id", nullable = false)
    private Material material;

    @Size(max = 36)
    @NotNull
    @Column(name = "source_id", nullable = false, length = 36)
    private String sourceId;

    @Size(max = 255)
    @NotNull
    @Column(name = "source_material_code", nullable = false)
    private String sourceMaterialCode;

    @NotNull
    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

    @Size(max = 255)
    @NotNull
    @Column(name = "created_by", nullable = false)
    @CreatedBy
    private String createdBy;

    @NotNull
    @Column(name = "created_time", nullable = false)
    @CreatedDate
    private LocalDateTime createdTime;

    @Size(max = 255)
    @NotNull
    @Column(name = "modified_by", nullable = false)
    @LastModifiedBy
    private String modifiedBy;

    @NotNull
    @Column(name = "modified_time", nullable = false)
    @LastModifiedDate
    private LocalDateTime modifiedTime;

}