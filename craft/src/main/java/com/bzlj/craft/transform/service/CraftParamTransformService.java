package com.bzlj.craft.transform.service;

import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.ParameterDefinitionExtendRepository;
import com.bzlj.craft.repository.ParameterDefinitionRepository;
import com.bzlj.craft.repository.StepParameterRepository;
import com.bzlj.craft.repository.WorkStepRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.transform.cache.ParamCacheManagerService;
import com.bzlj.craft.transform.enums.ParamConfirmed;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-06-06 15:04
 */
@Service
@Slf4j
public class CraftParamTransformService {

    @Autowired
    private ParamCacheManagerService cacheManager;

    @Autowired
    private ISurveillanceService surveillanceService;

    @Autowired
    private WorkStepRepository workStepRepository;

    @Autowired
    private StepParameterRepository stepParameterRepository;

    @Autowired
    private ParameterDefinitionRepository parameterDefinitionRepository;

    @Autowired
    private ParameterDefinitionExtendRepository parameterDefinitionExtendRepository;
    public void craftParamsTransform(String json) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        String matNo = jsonNode.get("matNo").asText();
        String orderNo = jsonNode.get("orderNo").asText();
        //从缓存中获取确认电文
        ParamConfirmed processConfirmed = cacheManager.isProcessConfirmed(matNo, orderNo);
        switch (processConfirmed) {
            case pending -> {
                cacheManager.addPendingProcessParams(matNo, orderNo, jsonNode);
            }
            case received -> {
                generateParams(jsonNode.get("planNo").asText(), (ArrayNode) jsonNode.get("craftParams"));
                //处理
                cacheManager.removeConfirmedParamsCache(matNo, orderNo);
            }
            case abnormal -> cacheManager.removeConfirmedParamsCache(matNo, orderNo);

        }

    }

    public void generateParams(String pono, ArrayNode craftParams) {
        SearchCondition condition = new SearchCondition();
        condition.setOpenProps(Lists.newArrayList("step"));
        condition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskCode", pono, null, SearchItem.Operator.EQ))
                .item(new SearchItem("deleted", false, null, SearchItem.Operator.EQ)).build());
        ProductionTask task = surveillanceService.findOne(condition);
        if (Objects.isNull(task)) {
            throw new RuntimeException(String.format("任务不存在；任务号：%s", pono));
        }
        if (Objects.isNull(craftParams) || craftParams.isEmpty()) {
            throw new RuntimeException("缺少工艺参数");
        }
        //查询工步以及执行工步
        ProcessStep step = task.getStep();
        WorkStep workStep = workStepRepository.findFirstByStepIdAndTaskTaskIdAndDeleted(step.getId(), task.getTaskId(), false);
        Map<String, StepParameter> stepParameterMap = findStepParameterMap(step);

        Map<String, ParameterDefinition> parameterDefMap = findParameterDefMap(workStep);
        List<StepParameter> stepParameters = new ArrayList<>();

        List<ParameterDefinition> parameterDefinitions = new ArrayList<>();
        Map<String, ParameterDefinitionExtend> parameterDefinitionExtendMap = new HashMap<>();
        final int[] order = {1};
        //查询执行工序
        craftParams.iterator().forEachRemaining(craftParam -> {
            stepParameters.add(buildStepParameter(stepParameterMap, craftParam,step, order[0]));
            ParameterDefinition parameterDefinition = buildParameter(parameterDefMap, craftParam, workStep);
            parameterDefinitions.add(parameterDefinition);
            if (Objects.nonNull(craftParam.get("extendAttr"))) {
                ParameterDefinitionExtend parameterDefinitionExtend = new ParameterDefinitionExtend();
                parameterDefinitionExtend.setExtendAttr(JsonUtils.toMap(craftParam.get("extendAttr")));
                parameterDefinitionExtendMap.put(parameterDefinition.getParamName(), parameterDefinitionExtend);
            }
            order[0]++;
        });
        stepParameterRepository.saveAll(stepParameters);
        List<ParameterDefinition> paramDefs = parameterDefinitionRepository.saveAll(parameterDefinitions);

        List<ParameterDefinitionExtend> extendList = paramDefs.stream().map(paramDef -> {
            String paramName = paramDef.getParamName();
            ParameterDefinitionExtend parameterDefinitionExtend = parameterDefinitionExtendMap.get(paramName);
            if (Objects.nonNull(parameterDefinitionExtend)) {
                parameterDefinitionExtend.setParamDefId(paramDef.getParamDefId());
            }
            return parameterDefinitionExtend;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(extendList)){
            parameterDefinitionExtendRepository.saveAll(extendList);
        }
    }

    /**
     * 查询工步下的工步参数，按照名称映射
     * @param step
     * @return
     */
    private Map<String,StepParameter> findStepParameterMap(ProcessStep step){
        //查询工步参数
        List<StepParameter> stepParameters = stepParameterRepository.findByStepIdOrderByCreatedTime(step.getId());
        Map<String, StepParameter> stepParameterMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(stepParameters)){
            stepParameterMap = Maps.uniqueIndex(stepParameters, StepParameter::getParamName);
        }
        return stepParameterMap;
    }

    /**
     * 查询执行工步下的工步参数，按照名称映射
     * @param workStep
     * @return
     */
    private Map<String,ParameterDefinition> findParameterDefMap(WorkStep workStep){
        //查询工步参数
        List<ParameterDefinition> parameterDefinitions = parameterDefinitionRepository.findByWorkStepWorkStepId(workStep.getWorkStepId());
        Map<String, ParameterDefinition> parameterDefinitionMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(parameterDefinitions)){
            parameterDefinitionMap = Maps.uniqueIndex(parameterDefinitions, ParameterDefinition::getParamName);
        }
        return parameterDefinitionMap;
    }

    /**
     * 构建保存更新的工艺参数
     * @param stepParameterMap
     * @param craftParam
     * @param step
     * @param order
     * @return
     */
    private StepParameter buildStepParameter(Map<String,StepParameter> stepParameterMap, JsonNode craftParam,ProcessStep step,int order) {
        StepParameter stepParameter = stepParameterMap.getOrDefault(craftParam.get("paramName").asText(), new StepParameter());
        stepParameter.setStep(step);
        stepParameter.setParamName(craftParam.get("paramName").asText());
        stepParameter.setParamOrder(order);
        return stepParameter;
    }

    /**
     * 构建保存更新的执行工艺参数
     * @param parameterDefMap
     * @param craftParam
     * @param workStep
     * @return
     */
    private ParameterDefinition buildParameter(Map<String,ParameterDefinition> parameterDefMap, JsonNode craftParam,WorkStep workStep) {
        ParameterDefinition parameterDefinition = parameterDefMap.getOrDefault(craftParam.get("paramName").asText(), new ParameterDefinition());
        parameterDefinition.setParamName(craftParam.get("paramName").asText());
        if (Objects.nonNull(craftParam.get("maxValue"))) {
            parameterDefinition.setMaxValue(new BigDecimal(craftParam.get("maxValue").asText()));
        }
        if (Objects.nonNull(craftParam.get("minValue"))) {
            parameterDefinition.setMinValue(new BigDecimal(craftParam.get("minValue").asText()));
        }
        if (Objects.nonNull(craftParam.get("targetValue"))) {
            parameterDefinition.setTargetValue(craftParam.get("targetValue").asText());
        }
        parameterDefinition.setWorkStep(workStep);
        return parameterDefinition;
    }

    public void paramAck(String json) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        String matNo = jsonNode.get("matNo").asText();
        String orderNo = jsonNode.get("orderNo").asText();
        String flag = jsonNode.get("replyFlag").asText();
        String replyDesc = jsonNode.get("replyDesc").asText();
        if (StringUtils.isBlank(matNo) || StringUtils.isBlank(orderNo) || StringUtils.isBlank(flag)) {
            throw new RuntimeException(String.format("G1MTTB:缺少参数；入口材料号：%s,合同号：%s,应答标记：%s", matNo, orderNo, flag));
        }
        if (!StringUtils.equals(flag, "0") && !StringUtils.equals(flag, "1")) {
            throw new RuntimeException(String.format("G1MTTB:参数错误；应答标识：%s", flag));
        }
        ParamConfirmed confirmed = StringUtils.equals(flag, "0") ? ParamConfirmed.received : ParamConfirmed.abnormal;
        //从缓存中获取工艺参数
        JsonNode paramNode = cacheManager.getAndRemovePendingParam(matNo, orderNo);
        if (Objects.isNull(paramNode)) {
            //确认电文先放入缓存
            cacheManager.addConfirmedParamsCache(matNo, orderNo, confirmed);
            return;
        }
        //如果存在确认电文，则将当前电文发送到kafka
        switch (confirmed) {
            case received ->  generateParams(paramNode.get("planNo").asText(), (ArrayNode) paramNode.get("craftParams"));
            case abnormal -> log.info("工艺参数: 应答异常，异常原因：{}", replyDesc);

        }
    }
}
