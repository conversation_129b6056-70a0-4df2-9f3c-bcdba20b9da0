package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.StepParameter;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StepParameterRepository extends BaseRepository<StepParameter, String> {
    List<StepParameter> findByStepIdOrderByCreatedTime(String stepId);

    /**
     * 统计指定工步下的参数数量
     * @param stepId 工步ID
     * @return 参数数量
     */
    int countByStepId(String stepId);
}