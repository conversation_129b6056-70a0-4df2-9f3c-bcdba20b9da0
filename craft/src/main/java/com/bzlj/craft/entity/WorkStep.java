package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "work_step")
public class WorkStep implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "work_step_id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String workStepId;

    @Size(max = 100)
    @NotNull
    @Column(name = "work_step_name", nullable = false, length = 100)
    private String workStepName;

    @Column(name = "is_subtask")
    private Boolean isSubtask;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_work_step_id")
    private WorkStep parentWorkStep;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "step_id", nullable = false)
    private ProcessStep step;

    @Column(name = "operation_guide")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> operationGuide;

    @Column(name = "required_attachments")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> requiredAttachments;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "status_code")
    private SysDictItem statusCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id")
    private ProductionTask task;

    @Column(name = "work_step_order")
    private Integer workStepOrder;


    /**
     * 逻辑删除 0 未删除 1删除
     */
    @Column(name = "deleted")
    private Boolean deleted = false;
}