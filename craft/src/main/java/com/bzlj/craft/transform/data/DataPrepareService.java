package com.bzlj.craft.transform.data;

import com.bzlj.craft.entity.Plant;
import com.bzlj.craft.entity.SysDictItem;
import com.bzlj.craft.repository.PlantRepository;
import com.bzlj.craft.repository.SysDictItemRepository;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class DataPrepareService {
    private final Map<String, Object> status_dict_map = new ConcurrentHashMap<>();

    private final Map<String, Object> plant_map = new ConcurrentHashMap<>();

    private final String dictCode = "TASK_STATUS";

    @Autowired
    PlantRepository plantRepository;

    @Autowired
    SysDictItemRepository sysDictItemRepository;


    @PostConstruct
    public void init() {
        List<SysDictItem> sysDictItems = sysDictItemRepository.findByDictCodeDictCode(dictCode);
        if(!CollectionUtils.isEmpty(sysDictItems)){
            sysDictItems.forEach(sysDictItem -> {
                status_dict_map.put(sysDictItem.getItemCode(), sysDictItem);
            });
            log.info("字典数据预加载完成，共加载 {} 条数据",sysDictItems.size());
        }
        List<Plant> plants = plantRepository.findAll();
        if(!CollectionUtils.isEmpty(plants)){
            plants.forEach(plant ->
                    plant_map.put(plant.getPlantCode(), plant)
            );
            log.info("字典数据预加载完成，共加载 {} 条数据",plants.size());
        }
        //工序导入
    }

    public SysDictItem getStatusDictItem(String statusCode){
        SysDictItem sysDictItem = (SysDictItem) status_dict_map.get(statusCode);
        if(Objects.isNull(sysDictItem)){
            SysDictItem dict = sysDictItemRepository.findFirstByItemCode(statusCode);
            if(Objects.nonNull(dict)){
                status_dict_map.put(dict.getItemCode(),dict);
                return dict;
            }
            return null;
        }
        return sysDictItem;
    }

    public Plant getPlant(String plantCode){
        Plant plant = (Plant) plant_map.get(plantCode);
        if(Objects.isNull(plant)){
            plant = plantRepository.findFirstByPlantCode(plantCode);
            if(Objects.nonNull(plant)){
                plant_map.put(plant.getPlantCode(),plant);
                return plant;
            }
            return null;
        }
        return plant;
    }

    public void setPlat(Plant plant){
        plant_map.put(plant.getPlantCode(),plant);
    }



}
