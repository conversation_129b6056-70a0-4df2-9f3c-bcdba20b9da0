package com.bzlj.craft.transform.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IMaterialService;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.enums.DictCode;
import com.bzlj.craft.enums.MaterialAttrType;
import com.bzlj.craft.repository.MaterialAttrRepository;
import com.bzlj.craft.repository.ProduceReportLoadMaterialRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ISysDictItemService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 上料记录转换处理器
 * <p>
 * 负责处理上料记录消息的转换和处理，主要功能包括：
 * 1. 接收上料记录数据消息
 * 2. 解析物料信息并创建物料实体
 * 3. 处理物料属性和上料报告关联关系
 * 4. 支持批量上料记录数据处理
 * 5. 管理生产报告中的上料物料信息
 * </p>
 * <p>
 * 特别适用于特冶感应炉原料消耗实绩的处理场景
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@MessageHandler(messageType = "load_material", desc = "上料记录转化")
public class LoadMaterialTransformHandle extends CommonHandler<String> {

    /**
     * 电报仓储，用于管理电报数据
     */
    @Autowired
    private TelegramRepository telegramRepository;

    /**
     * 物料服务，用于管理物料相关业务逻辑
     */
    @Autowired
    private IMaterialService materialService;

    /**
     * 系统字典项服务，用于获取字典数据
     */
    @Autowired
    private ISysDictItemService sysDictItemService;

    /**
     * 监控服务，用于查询和管理生产任务
     */
    @Autowired
    private ISurveillanceService surveillanceService;

    /**
     * 物料属性仓储，用于管理物料属性数据
     */
    @Autowired
    private MaterialAttrRepository materialAttrRepository;

    /**
     * 生产报告上料物料仓储，用于管理上料物料报告数据
     */
    @Autowired
    private ProduceReportLoadMaterialRepository produceReportLoadMaterialRepository;

    /**
     * 转换处理上料记录消息
     * <p>
     * 接收上料记录的JSON消息，调用具体的上料记录转换处理方法
     * </p>
     *
     * @param s 上料记录的JSON字符串
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String s) {
        loadMaterialTransform(s);
    }

    /**
     * 处理历史遗留数据
     * <p>
     * 当前实现为空，暂无需处理历史遗留数据
     * </p>
     *
     * @param relationIds 关联ID列表
     */
    @Override
    public void dealLegacyData(List<String> relationIds) {
        // 暂无需处理历史遗留数据
    }

    /**
     * 获取关联ID列表
     * <p>
     * 当前实现返回空列表，暂无关联ID需要处理
     * </p>
     *
     * @param s 消息载荷
     * @return 空的关联ID列表
     */
    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }

    /**
     * 上料记录转换处理方法
     * <p>
     * 处理上料记录JSON数据的核心方法，包括以下步骤：
     * 1. 解析JSON数组，提取物料编码和任务编码
     * 2. 查询相关的生产任务和物料数据
     * 3. 构建物料实体、属性和上料报告映射关系
     * 4. 保存上料物料数据
     * </p>
     *
     * @param json 上料记录的JSON字符串（数组格式）
     * @throws RuntimeException 当任务不存在时抛出异常
     */
    public void loadMaterialTransform(String json) {
        ArrayNode jsonNode = (ArrayNode) JsonUtils.toJsonNode(json);
        List<String> materialCodes = new ArrayList<>();
        Set<String> taskCodes = new HashSet<>();

        // 提取物料编码和任务编码
        jsonNode.iterator().forEachRemaining(item -> {
            materialCodes.add(item.get("materialCode").asText());
            taskCodes.add(item.get("taskCode").asText());
        });

        // 查询生产任务
        List<ProductionTask> tasks = surveillanceService.findByTaskCodes(taskCodes);
        if(CollectionUtils.isEmpty(tasks)){
            throw new RuntimeException(String.format("任务不存在；任务号：%s", tasks));
        }

        // 构建映射关系
        ImmutableMap<String, ProductionTask> taskMap = Maps.uniqueIndex(tasks, ProductionTask::getTaskCode);
        Map<String, Material> materialMap = findMaterialMap(materialCodes);
        List<SysDictItem> attrType = sysDictItemService.findEntityByDictCode(DictCode.MATERIAL_ATTR_TYPE.getCode());
        ImmutableMap<String, SysDictItem> attrTypeMap = Maps.uniqueIndex(attrType, SysDictItem::getItemCode);

        // 初始化数据容器
        Map<String, MaterialAttr> materialAttrMap = new HashMap<>();
        List<Material> materials = new ArrayList<>();
        Map<String, ProduceReportLoadMaterial> LoadMaterialMap = new HashMap<>();
        int[] order = {1};

        // 处理每个上料记录项
        jsonNode.iterator().forEachRemaining(item -> {
            Material material = buildMaterial(materialMap, item, attrTypeMap, materialAttrMap);
            materials.add(material);
            ProductionTask productionTask = taskMap.get(item.get("taskCode").asText());
            ProduceReportLoadMaterial produceReportLoadMaterial = buildLoadMaterial(productionTask, item, order[0]);
            LoadMaterialMap.put(material.getMaterialCode(), produceReportLoadMaterial);
            order[0]++;
        });

        // 保存上料物料数据
        saveLoadMaterial(materials, materialAttrMap, LoadMaterialMap);
    }

    public void saveLoadMaterial(List<Material> materials, Map<String, MaterialAttr> materialAttrMap, Map<String, ProduceReportLoadMaterial> LoadMaterialMap) {
        if (!CollectionUtils.isEmpty(materials)) {
            List<Material> saveMaterials = materialService.batchInsertEntity(materials);
            ImmutableMap<String, Material> newMaterialMap = Maps.uniqueIndex(saveMaterials, Material::getMaterialCode);
            materialAttrMap.forEach((materialCode, materialAttr) -> {
                Material material = newMaterialMap.get(materialCode);
                materialAttr.setMaterial(material);
            });
            List<MaterialAttr> materialAttrs = materialAttrRepository.saveAll(materialAttrMap.values());
            LoadMaterialMap.forEach((materialCode, loadMaterial) -> {
                MaterialAttr materialAttr = materialAttrMap.get(materialCode);
                loadMaterial.setMaterialAttr(materialAttr);
                Material material = newMaterialMap.get(materialCode);
                loadMaterial.setMaterial(material);
            });
            produceReportLoadMaterialRepository.saveAll(LoadMaterialMap.values());
        }
    }

    private Map<String, Material> findMaterialMap(List<String> materialCodes) {
        SearchCondition condition = new SearchCondition();
        condition.setOpenProps(Lists.newArrayList("materialAttrs", "materialAttrs.attrType"));
        condition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("materialCode", materialCodes, null, SearchItem.Operator.IN))
                .build());
        List<Material> materials = materialService.findAllEntityWithCondition(condition);
        return Maps.uniqueIndex(materials, Material::getMaterialCode);
    }

    private Material buildMaterial(Map<String, Material> materialMap, JsonNode materialNode, Map<String, SysDictItem> sysDictItemMap, Map<String, MaterialAttr> materialAttrMap) {
        Material material = materialMap.getOrDefault(materialNode.get("materialCode").asText(), new Material());
        material.setMaterialCode(materialNode.get("materialCode").asText());
        material.setMaterialName(materialNode.get("materialName").asText());
        material.setMaterialType("原料");
        String bitchNo = materialNode.get("bitchNo").asText();
        if(Objects.nonNull(materialNode.get("brand"))){
            material.setBrand((materialNode.get("brand").asText()));
        }
        if(Objects.nonNull(materialNode.get("heatNumber"))){
            material.setHeatNumber((materialNode.get("heatNumber").asText()));
        }
        Set<MaterialAttr> materialAttrs = material.getMaterialAttrs();
        if (!CollectionUtils.isEmpty(materialAttrs)) {
            //查看当前物料是否包含给定批次号，不包含则新增一个批次的规格
            List<MaterialAttr> specificationAttrs = materialAttrs.stream()
                    .filter(materialAttr ->
                            materialAttr.getAttrType().getItemCode().equals(MaterialAttrType.specification_attr.getCode()) &&
                                    !CollectionUtils.isEmpty(materialAttr.getAttr()) &&
                                    materialAttr.getAttr().containsKey("bitchNo") &&
                                    materialAttr.getAttr().get("bitchNo").equals(bitchNo)
                    ).toList();
            if (!CollectionUtils.isEmpty(specificationAttrs)) {
                materialAttrMap.put(material.getMaterialCode(), specificationAttrs.getFirst());
                return material;
            }
        }
        MaterialAttr materialAttr = new MaterialAttr();
        materialAttr.setMaterial(material);
        SysDictItem sysDictItem = sysDictItemMap.get(MaterialAttrType.specification_attr.getCode());
        materialAttr.setAttrType(sysDictItem);
        Map<String, Object> map = new HashMap<>();
        map.put("bitchNo", bitchNo);
        //宝武方有且仅有一个库位T0BA,所以不用按照库位区分物料
        if (Objects.nonNull(materialNode.get("yardPlaceNo"))) {
            map.put("yardPlaceNo", materialNode.get("yardPlaceNo").asText());
        }
        materialAttr.setAttr(map);
        materialAttrMap.put(material.getMaterialCode(), materialAttr);
        return material;

    }

    private ProduceReportLoadMaterial buildLoadMaterial(ProductionTask productionTask, JsonNode item, int order) {
        ProduceReportLoadMaterial produceReportLoadMaterial = new ProduceReportLoadMaterial();
        if (Objects.nonNull(item.get("weight"))) {
            produceReportLoadMaterial.setDosage(new BigDecimal(item.get("weight").asText()));
        }
        produceReportLoadMaterial.setOrder(order);
        produceReportLoadMaterial.setTask(productionTask);
        if (Objects.nonNull(item.get("heatNumber"))) {
            produceReportLoadMaterial.setHeatNo(item.get("heatNumber").asText());
        }
        return produceReportLoadMaterial;
    }

    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }
}
