package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "parameter_definition_extend")
public class ParameterDefinitionExtend {
    @Id
    @Size(max = 36)
    @Column(name = "id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String id;

    @Size(max = 36)
    @Column(name = "param_def_id", nullable = false, length = 36)
    private String paramDefId;

    @Column(name = "extend_attr")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> extendAttr;

    @NotNull
    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

}