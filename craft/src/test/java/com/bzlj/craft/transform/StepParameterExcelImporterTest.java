package com.bzlj.craft.transform;

import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.*;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.transform.service.ExcelImportPolicy;
import com.bzlj.craft.transform.service.impl.StepParameterExcelImporter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.testng.Assert.*;

public class StepParameterExcelImporterTest extends BaseTestNGTest {

    @Mock
    private CraftProcessRepository craftProcessRepository;

    @Mock
    private ProcessStepRepository processStepRepository;

    @Mock
    private StepParameterRepository stepParameterRepository;

    @Mock
    private DataPrepareService dataPrepareService;

    @InjectMocks
    private StepParameterExcelImporter stepParameterExcelImporter;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testParseExcel() throws IOException {
        // 创建测试Excel数据
        ByteArrayInputStream inputStream = createTestExcel();

        // 模拟依赖对象
        CraftProcess mockProcess = createMockCraftProcess();
        ProcessStep mockStep = createMockProcessStep();
        SysDictItem mockParamType = createMockParamType();

        when(craftProcessRepository.findFirstByProcessCode("PROCESS001")).thenReturn(mockProcess);
        when(processStepRepository.findByProcessIdOrderByStepOrderAsc(anyString())).thenReturn(List.of(mockStep));
        when(dataPrepareService.getParamTypeDictItem("NUMBER")).thenReturn(mockParamType);
        // 模拟数据库中已有3个参数，新导入的参数应该从4开始编号
        when(stepParameterRepository.countByStepId(anyString())).thenReturn(3);

        // 执行测试
        List<StepParameter> result = stepParameterExcelImporter.parseExcel(inputStream);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.size(), 2);

        StepParameter param1 = result.get(0);
        assertEquals(param1.getParamCode(), "PARAM001");
        assertEquals(param1.getParamName(), "温度");
        assertEquals(param1.getParamType().getItemCode(), "NUMBER");
        assertEquals(param1.getParamOrder(), Integer.valueOf(4)); // 数据库已有3个，新的从4开始

        StepParameter param2 = result.get(1);
        assertEquals(param2.getParamCode(), "PARAM002");
        assertEquals(param2.getParamName(), "压力");
        assertEquals(param2.getParamType().getItemCode(), "NUMBER");
        assertEquals(param2.getParamOrder(), Integer.valueOf(5)); // 第二个参数是5
    }

    @Test
    public void testImportFromExcel() throws IOException {
        // 创建测试Excel数据
        ByteArrayInputStream inputStream = createTestExcel();

        // 模拟依赖对象
        CraftProcess mockProcess = createMockCraftProcess();
        ProcessStep mockStep = createMockProcessStep();
        SysDictItem mockParamType = createMockParamType();

        when(craftProcessRepository.findFirstByProcessCode("PROCESS001")).thenReturn(mockProcess);
        when(processStepRepository.findByProcessIdOrderByStepOrderAsc(anyString())).thenReturn(List.of(mockStep));
        when(dataPrepareService.getParamTypeDictItem("NUMBER")).thenReturn(mockParamType);
        when(stepParameterRepository.countByStepId(anyString())).thenReturn(3);
        when(stepParameterRepository.saveAll(any())).thenAnswer(invocation -> invocation.getArgument(0));

        // 执行测试
        ExcelImportPolicy.ImportResult<StepParameter> result = stepParameterExcelImporter.importFromExcel(inputStream);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getSize(), Integer.valueOf(2));
        assertNotNull(result.getResults());
        assertEquals(result.getResults().size(), 2);
    }

    @Test
    public void testParamOrderCalculation() throws IOException {
        // 测试参数顺序计算逻辑
        ByteArrayInputStream inputStream = createTestExcel();

        CraftProcess mockProcess = createMockCraftProcess();
        ProcessStep mockStep = createMockProcessStep();
        SysDictItem mockParamType = createMockParamType();

        when(craftProcessRepository.findFirstByProcessCode("PROCESS001")).thenReturn(mockProcess);
        when(processStepRepository.findByProcessIdOrderByStepOrderAsc(anyString())).thenReturn(List.of(mockStep));
        when(dataPrepareService.getParamTypeDictItem("NUMBER")).thenReturn(mockParamType);

        // 模拟数据库中已有5个参数
        when(stepParameterRepository.countByStepId(anyString())).thenReturn(5);

        List<StepParameter> result = stepParameterExcelImporter.parseExcel(inputStream);

        // 验证参数顺序从6开始
        assertEquals(result.get(0).getParamOrder(), Integer.valueOf(6));
        assertEquals(result.get(1).getParamOrder(), Integer.valueOf(7));
    }

    private ByteArrayInputStream createTestExcel() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("工艺参数");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("工序");
        headerRow.createCell(1).setCellValue("参数Code");
        headerRow.createCell(2).setCellValue("参数名称");
        headerRow.createCell(3).setCellValue("参数类型");

        // 创建测试数据
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue("PROCESS001");
        row1.createCell(1).setCellValue("PARAM001");
        row1.createCell(2).setCellValue("温度");
        row1.createCell(3).setCellValue("NUMBER");

        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue("PROCESS001");
        row2.createCell(1).setCellValue("PARAM002");
        row2.createCell(2).setCellValue("压力");
        row2.createCell(3).setCellValue("NUMBER");

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    private CraftProcess createMockCraftProcess() {
        CraftProcess process = new CraftProcess();
        process.setId("process-id-001");
        process.setProcessCode("PROCESS001");
        process.setProcessName("测试工序");
        process.setProcessOrder(1);
        process.setInputOutputSpec(Map.of());
        return process;
    }

    private ProcessStep createMockProcessStep() {
        ProcessStep step = new ProcessStep();
        step.setId("step-id-001");
        step.setStepCode("STEP001");
        step.setStepName("测试工步");
        step.setStepOrder(1);
        step.setParamConfig(Map.of());
        step.setQualityStandard(Map.of());
        return step;
    }

    private SysDictItem createMockParamType() {
        SysDictItem dictItem = new SysDictItem();
        dictItem.setItemCode("NUMBER");
        dictItem.setItemName("数值型");
        dictItem.setSortOrder(1);
        dictItem.setIsActive(true);
        return dictItem;
    }
}
