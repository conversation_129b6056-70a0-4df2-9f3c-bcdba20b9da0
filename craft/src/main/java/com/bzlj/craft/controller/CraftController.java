package com.bzlj.craft.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.craft.dto.CraftProcessDTO;
import com.bzlj.craft.dto.PlantDTO;
import com.bzlj.craft.dto.ProcessStepDTO;
import com.bzlj.craft.dto.StepParameterDTO;
import com.bzlj.craft.service.ICraftService;
import com.bzlj.craft.transform.service.ExcelImportFactory;
import com.bzlj.craft.transform.service.ExcelImportPolicy;
import com.bzlj.craft.util.ExcelTemplateUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import java.util.List;

/**
 * 工艺相关接口
 * <AUTHOR>
 * @description: 工艺相关接口
 * @date 2025-03-10 13:35
 */
@RestController
@RequestMapping("/craft")
@RequiredArgsConstructor
public class CraftController {
    @Autowired
    private final ICraftService craftService;


    /**
     * 查询所有分厂
     * @return
     */
    @GetMapping("/findPlantList")
    public UnifyResponse<List<PlantDTO>> findPlantList() {
        return UnifyResponse.success(craftService.findPlantList());
    }

    /**
     * 根据分厂查询所有工工序
     * @param plantCode 工厂code
     * @return
     */
    @GetMapping("/findProcessByPlantCode")
    public UnifyResponse<List<CraftProcessDTO>> findProcessByPlantCode(@RequestParam(name = "plantCode",required = false) String plantCode) {
        return UnifyResponse.success(craftService.findProcessByPlantCode(plantCode));
    }

    /**
     * 根据工序查询所有工步
     * @param processId 工序id
     * @return
     */
    @GetMapping("/findProcessStepByProcessId")
    public UnifyResponse<List<ProcessStepDTO>> findProcessStepByProcessId(@RequestParam(name = "processId") String processId) {
        return UnifyResponse.success(craftService.findProcessStepByProcessId(processId));
    }

    /**
     * 根据工步查询所有工艺参数
     * @param stepId 工步id
     * @return
     */
    @GetMapping("/findStepParameterByStepId")
    public UnifyResponse<List<StepParameterDTO>> findStepParameterByStepId(@RequestParam(name = "stepId") String stepId) {
        return UnifyResponse.success(craftService.findStepParameterByStepId(stepId));
    }

    /**
     * 基础数据导入
     * @param file
     * @return
     */
    @PostMapping("/data/import")
    public UnifyResponse<ExcelImportPolicy.ImportResult> importExcel(@RequestParam("file") MultipartFile file,  @RequestParam("type") String type) {
        try {
            ExcelImportPolicy policy = ExcelImportFactory.getPolicy(type);
            ExcelImportPolicy.ImportResult result = policy.importFromExcel(file.getInputStream());
            return UnifyResponse.success(result);
        } catch (Exception e) {
            return UnifyResponse.failed("导入失败：" + e.getMessage());
        }
    }

}
