package com.bzlj.craft.service.impl;


import com.bzlj.craft.entity.*;
import com.bzlj.craft.nebula.constants.CommonNGQLConstants;
import com.bzlj.craft.nebula.entity.edge.InputMaterial;
import com.bzlj.craft.nebula.entity.edge.OutputMaterial;
import com.bzlj.craft.nebula.entity.edge.UseEquip;
import com.bzlj.craft.nebula.entity.tag.Equip;
import com.bzlj.craft.nebula.entity.tag.Task;
import com.bzlj.craft.nebula.util.NebulaUtil;
import com.bzlj.craft.query.QualityTraceQuery;
import com.bzlj.craft.repository.EquipmentRepository;
import com.bzlj.craft.repository.MaterialRepository;
import com.bzlj.craft.repository.ProductionTaskRepository;
import com.bzlj.craft.service.IQualityTracingService;
import com.bzlj.craft.util.JsonUtils;
import com.bzlj.craft.vo.qualitytrace.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;


/**
 * 质量追溯接口实现
 *
 * <AUTHOR>
 * @date 2025/3/24 11:15
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QualityTracingServiceImpl implements IQualityTracingService {
    private final ProductionTaskRepository productionTaskRepository;
    private final MaterialRepository materialRepository;
    private final EquipmentRepository equipmentRepository;

    /**
     * 初始化nebula数据
     *
     * <AUTHOR>
     * @date 2025/3/24 14:13
     */
    @Override
    @Transactional(rollbackFor = Exception.class) //如果需要使用jpa特性必须要加上事务注解
    public Boolean initNebulaData() {
        // 刷入物料
        List<Material> allMaterial = materialRepository.findAll();
        List<com.bzlj.craft.nebula.entity.tag.Material> materialList = allMaterial.parallelStream()
                .map(material -> {
                    com.bzlj.craft.nebula.entity.tag.Material nebulaMaterial = new com.bzlj.craft.nebula.entity.tag.Material();
                    BeanUtils.copyProperties(material, nebulaMaterial);
                    return nebulaMaterial;
                }).toList();
        NebulaUtil.batchInsert(materialList);
        // 刷入设备
        List<Equipment> allEquipment = equipmentRepository.findAll();
        List<Equip> equipList = allEquipment.parallelStream().map(equipment -> {
            Equip equip = new Equip();
            equip.setEquipId(equipment.getId());
            equip.setEquipName(equipment.getName());
            equip.setEquipCode(equipment.getCode());
            return equip;
        }).toList();
        NebulaUtil.batchInsert(equipList);
        // 刷入任务
        SysDictItem sysDictItem = new SysDictItem();
        sysDictItem.setItemCode("completed");
        List<ProductionTask> completedTasks = productionTaskRepository.findByStatusCodeAndDeleted(sysDictItem, false);
        completedTasks.forEach(productionTask -> {
            Task task = new Task();
            BeanUtils.copyProperties(productionTask, task);
            task.setPlantId(productionTask.getPlant().getPlantCode());
            NebulaUtil.insert(task);
            // 构建关系 任务-物料
            for (TaskMaterial taskMaterial : productionTask.getTaskMaterials()) {
                if (taskMaterial.getRelationType()) {
                    NebulaUtil.insertEdge(task.getTaskId(),
                            taskMaterial.getMaterial().getMaterialId(),
                            new OutputMaterial());
                } else {
                    NebulaUtil.insertEdge(taskMaterial.getMaterial().getMaterialId(),
                            task.getTaskId(),
                            new InputMaterial());

                }
            }
            // 构建关系 任务-设备
            for (TaskEquipment taskEquipment : productionTask.getTaskEquipments()) {
                NebulaUtil.insertEdge(task.getTaskId(), taskEquipment.getEquip().getId(), new UseEquip());
            }
        });
        return Boolean.TRUE;
    }

    /**
     * 获取质量追溯信息
     *
     * @param query
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    @Override
    public QualityTraceVO getQualityTracing(QualityTraceQuery query) {
        QualityTraceVO result = new QualityTraceVO();
        if (StringUtils.isBlank(query.getPlantId())) {
            queryAll(query, result);
        } else {
            queryByPlantId(query, result);
        }
        return result;
    }

    /**
     * 厂内程追溯
     *
     * @param query  查询条件
     * @param result 返回结果
     * <AUTHOR>
     * @date 2025/6/23 15:21
     */
    private void queryByPlantId(QualityTraceQuery query, QualityTraceVO result) {
        //1. 获取产出物料的任务
        List<TraceTaskVO> selectTasks = getSelectTasks(query);
        result.setSelectTasks(selectTasks);
        final ConcurrentLinkedDeque<TracePathVO> paths = new ConcurrentLinkedDeque<>();
        final ConcurrentHashMap<String, Object> pathCache = new ConcurrentHashMap<>();
        final ConcurrentHashMap<String, TraceTaskVO> taskCache = new ConcurrentHashMap<>();
        for (TraceTaskVO selectTask : selectTasks) {
            taskCache.put(selectTask.getTaskId(), selectTask);
        }
        selectTasks.parallelStream().forEach(traceTaskVO -> {
            addPreviousTask(paths, traceTaskVO, query.getPlantId(), pathCache, taskCache);
            addNextTask(paths, traceTaskVO, query.getPlantId(), pathCache, taskCache);
        });
        result.setPath(paths.stream().toList());
    }

    /**
     * 全流程追溯
     *
     * @param query  查询条件
     * @param result 返回结果
     * <AUTHOR>
     * @date 2025/6/23 15:21
     */
    private void queryAll(QualityTraceQuery query, QualityTraceVO result) {
        //1. 获取产出物料的任务
        List<TraceTaskVO> selectTasks = getSelectTasks(query);
        result.setSelectTasks(selectTasks);
        final ConcurrentLinkedDeque<TracePathVO> paths = new ConcurrentLinkedDeque<>();
        final ConcurrentHashMap<String, Object> pathCache = new ConcurrentHashMap<>();
        final ConcurrentHashMap<String, TraceTaskVO> taskCache = new ConcurrentHashMap<>();
        for (TraceTaskVO selectTask : selectTasks) {
            taskCache.put(selectTask.getTaskId(), selectTask);
        }
        selectTasks.parallelStream().forEach(traceTaskVO -> {
            addPreviousTask(paths, traceTaskVO, null, pathCache, taskCache);
            addNextTask(paths, traceTaskVO, null, pathCache, taskCache);
        });
        result.setPath(paths.stream().toList());
    }

    /**
     * 查找后节点
     *
     * @param traceTaskVO 当前节点
     * @param pathCache   缓存
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private void addNextTask(ConcurrentLinkedDeque<TracePathVO> paths,
                             TraceTaskVO traceTaskVO,
                             String plantId,
                             ConcurrentHashMap<String, Object> pathCache,
                             ConcurrentHashMap<String, TraceTaskVO> taskCache) {
        String nGQL = StringUtils.isBlank(plantId) ?
                CommonNGQLConstants.getQueryQualityTraceNextPathByTaskIdNGQL(traceTaskVO.getTaskId()) :
                CommonNGQLConstants.getQueryQualityTraceNextPathByTaskIdAndPlantIdNGQL(plantId, traceTaskVO.getTaskId());
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> pathMap : execute) {
            TracePathVO path = new TracePathVO();
            dealStartNode(traceTaskVO, taskCache, pathMap, path);
            dealEndNode(traceTaskVO, taskCache, pathMap, path);
            // 去除环形路径
            String cacheKey = path.getStart().getTaskId() + "_" + path.getEnd().getTaskId();
            if (pathCache.containsKey(cacheKey)) {
                continue;
            }
            pathCache.put(cacheKey, path);
            paths.addLast(path);
            addNextTask(paths, path.getEnd(), plantId, pathCache, taskCache);
        }
    }

    /**
     * 查找前节点
     *
     * @param traceTaskVO 当前节点
     * @param pathCache   缓存
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private void addPreviousTask(ConcurrentLinkedDeque<TracePathVO> paths,
                                 TraceTaskVO traceTaskVO,
                                 String plantId,
                                 ConcurrentHashMap<String, Object> pathCache,
                                 ConcurrentHashMap<String, TraceTaskVO> taskCache) {
        String nGQL = StringUtils.isBlank(plantId) ? CommonNGQLConstants.getQueryQualityTracePrePathByTaskIdNGQL(traceTaskVO.getTaskId()) :
                CommonNGQLConstants.getQueryQualityTracePrePathByTaskIdAndPlantIdNGQL(plantId, traceTaskVO.getTaskId());
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> pathMap : execute) {
            TracePathVO path = new TracePathVO();
            dealStartNode(traceTaskVO, taskCache, pathMap, path);
            dealEndNode(traceTaskVO, taskCache, pathMap, path);
            // 去除环形路径
            String cacheKey = path.getStart().getTaskId() + "_" + path.getEnd().getTaskId();
            if (pathCache.containsKey(cacheKey)) {
                continue;
            }
            pathCache.put(cacheKey, path);
            paths.addFirst(path);
            addPreviousTask(paths, path.getStart(), plantId, pathCache, taskCache);
        }
    }

    /**
     * 处理结束节点
     *
     * @param traceTaskVO
     * @param taskCache
     * @param pathMap
     * @param path
     * <AUTHOR>
     * @date 2025/3/25 16:55
     */
    private void dealEndNode(TraceTaskVO traceTaskVO,
                             ConcurrentHashMap<String, TraceTaskVO> taskCache,
                             Map<String, Object> pathMap,
                             TracePathVO path) {
        Map<String, Object> startMap = (Map) pathMap.get("e");
        Map<String, Object> startProperties = (Map) startMap.get("properties");
        Map<String, Object> startTask = (Map) startProperties.get("task");
        TraceTaskVO startTraceTaskVO;
        if (taskCache.containsKey(startTask.get("taskId"))) {
            startTraceTaskVO = taskCache.get(startTask.get("taskId"));
            path.setEnd(startTraceTaskVO);
            return;
        }
        Map<String, Object> dataMap = Maps.newHashMap();
        startTask.forEach((k, v) -> {
            dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
        });
        startTraceTaskVO = JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceTaskVO.class);
        startTraceTaskVO.setInputMaterial(getTaskMaterial(traceTaskVO.getTaskId(), false));
        startTraceTaskVO.setOutputMaterial(getTaskMaterial(traceTaskVO.getTaskId(), true));
        startTraceTaskVO.setEquip(getTaskEquip(traceTaskVO.getTaskId()));
        taskCache.put(startTraceTaskVO.getTaskId(), startTraceTaskVO);
        path.setEnd(startTraceTaskVO);
    }

    /**
     * 处理开始节点
     *
     * @param traceTaskVO
     * @param taskCache
     * @param pathMap
     * @param path
     * <AUTHOR>
     * @date 2025/3/25 16:55
     */
    private void dealStartNode(TraceTaskVO traceTaskVO,
                               ConcurrentHashMap<String, TraceTaskVO> taskCache,
                               Map<String, Object> pathMap,
                               TracePathVO path) {
        Map<String, Object> startMap = (Map) pathMap.get("s");
        Map<String, Object> startProperties = (Map) startMap.get("properties");
        Map<String, Object> startTask = (Map) startProperties.get("task");
        TraceTaskVO startTraceTaskVO;
        if (taskCache.containsKey(startTask.get("taskId"))) {
            startTraceTaskVO = taskCache.get(startTask.get("taskId"));
            path.setStart(startTraceTaskVO);
            return;
        }
        Map<String, Object> dataMap = Maps.newHashMap();
        startTask.forEach((k, v) -> {
            dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
        });
        startTraceTaskVO = JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceTaskVO.class);
        startTraceTaskVO.setInputMaterial(getTaskMaterial(traceTaskVO.getTaskId(), false));
        startTraceTaskVO.setOutputMaterial(getTaskMaterial(traceTaskVO.getTaskId(), true));
        startTraceTaskVO.setEquip(getTaskEquip(traceTaskVO.getTaskId()));
        taskCache.put(startTraceTaskVO.getTaskId(), startTraceTaskVO);
        path.setStart(startTraceTaskVO);
    }

    /**
     * 获取产出物料的任务
     *
     * @param query 查询条件
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private List<TraceTaskVO> getSelectTasks(QualityTraceQuery query) {
        List<TraceTaskVO> result = Lists.newArrayList();
        String nGQL = StringUtils.isBlank(query.getPlantId()) ?
                CommonNGQLConstants.getQueryTaskByOutputMaterialCodeNGQL(query.getMaterialCode()) :
                CommonNGQLConstants.getQueryTaskByOutputMaterialCodeAndPlantIdNGQL(query.getPlantId(), query.getMaterialCode());
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> taskMap : execute) {
            Map<String, Object> t = (Map) taskMap.get("t");
            Map<String, Object> properties = (Map) t.get("properties");
            Map<String, Object> task = (Map) properties.get("task");
            Map<String, Object> dataMap = Maps.newHashMap();
            task.forEach((k, v) -> {
                dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
            });
            TraceTaskVO traceTaskVO = JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceTaskVO.class);
            traceTaskVO.setInputMaterial(getTaskMaterial(traceTaskVO.getTaskId(), false));
            traceTaskVO.setOutputMaterial(getTaskMaterial(traceTaskVO.getTaskId(), true));
            traceTaskVO.setEquip(getTaskEquip(traceTaskVO.getTaskId()));
            result.add(traceTaskVO);
        }
        return result;
    }

    /**
     * 获取任务设备
     *
     * @param taskId 任务id
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private List<TraceEquipVO> getTaskEquip(String taskId) {
        List<TraceEquipVO> result = Lists.newArrayList();
        String nGQL = CommonNGQLConstants.getQueryTaskEquipByTaskIdNGQL(taskId);
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> taskMap : execute) {
            Map<String, Object> e = (Map) taskMap.get("e");
            Map<String, Object> properties = (Map) e.get("properties");
            Map<String, Object> equip = (Map) properties.get("equip");
            Map<String, Object> dataMap = Maps.newHashMap();
            equip.forEach((k, v) -> {
                dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
            });
            TraceEquipVO traceEquipVO = JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceEquipVO.class);
            result.add(traceEquipVO);
        }
        return result;
    }


    /**
     * 获取任务物料
     *
     * @param taskId 任务id
     * @param type   true:产出物料 false:输入物料
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private List<TraceMaterialVO> getTaskMaterial(String taskId, boolean type) {
        List<TraceMaterialVO> result = Lists.newArrayList();
        String nGQL = type ? CommonNGQLConstants.getQueryTaskOutputMaterialByTaskIdNGQL(taskId)
                : CommonNGQLConstants.getQueryTaskInputMaterialByTaskIdNGQL(taskId);
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> taskMap : execute) {
            Map<String, Object> m = (Map) taskMap.get("m");
            Map<String, Object> properties = (Map) m.get("properties");
            Map<String, Object> material = (Map) properties.get("material");
            Map<String, Object> dataMap = Maps.newHashMap();
            material.forEach((k, v) -> {
                dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
            });
            result.add(JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceMaterialVO.class));
        }
        return result;
    }
}
