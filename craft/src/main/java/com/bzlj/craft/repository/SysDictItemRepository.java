package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.SysDictItem;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SysDictItemRepository extends BaseRepository<SysDictItem, String> {

    List<SysDictItem> findByDictCodeDictCode(String dictCode);

    SysDictItem findFirstByItemCode(String itemCode);

    long countByDictCodeDictCode(String dictCode);
}