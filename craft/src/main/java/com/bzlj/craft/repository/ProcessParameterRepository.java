package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.ProcessParameter;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProcessParameterRepository extends BaseRepository<ProcessParameter, String> {

    List<ProcessParameter> findByParamDefParamDefIdInAndTaskTaskId(List<String> paramDefIds, String taskId);
}