package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "step_parameter")
@EntityListeners(AuditingEntityListener.class)
public class StepParameter {
    @Id
    @Size(max = 32)
    @Column(name = "param_id", nullable = false, length = 32)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String paramId;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "step_id", nullable = false)
    private ProcessStep step;

    @Size(max = 50)
    @Column(name = "param_code", length = 50)
    private String paramCode;

    @Column(name = "param_name")
    private String paramName;

    @Column(name = "param_order", nullable = false)
    private Integer paramOrder;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "param_type")
    private SysDictItem paramType;

    @ColumnDefault("1")
    @Column(name = "version")
    private Integer version;

    @Size(max = 50)
    @Column(name = "created_by", nullable = false, length = 50)
    @CreatedBy
    private String createdBy;

    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "created_time")
    @CreatedDate
    private LocalDateTime createdTime;

    @Size(max = 50)
    @Column(name = "modified_by", length = 50)
    @LastModifiedBy
    private String modifiedBy;

    @Column(name = "modified_time")
    @LastModifiedDate
    private LocalDateTime modifiedTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "data_category")
    private SysDictItem dataCategory;

}